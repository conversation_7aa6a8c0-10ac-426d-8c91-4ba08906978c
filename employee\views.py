from django.shortcuts import render
from .models import employee

def home(request):
    return render(request, 'home.html')

def contact(request):
    employee_data=employee.objects.all()
    context={'employee_data':employee_data}
    return render(request, 'contact.html',context)

def employee_list(request):
    employee_data=employee.objects.all()
    context={'employee_data':employee_data}
    return render(request, 'employee_list.html',context)