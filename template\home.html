{% load static %}
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Modern Portfolio</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <!-- Font Awesome for icons (optional, but good for portfolio) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">Salina kunwar</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#skills">Skills</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#portfolio">Portfolio</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <section id="home" class="hero-section d-flex align-items-center text-center text-white">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <h1 class="display-2 mb-4">Hi, I'm Salina kunwar</h1>
                    <p class="lead mb-5">A passionate student with a love for Ai vibe coding.</p>
                    <a href="#portfolio" class="btn btn-primary btn-lg me-3">View My Work</a>
                    <a href="#contact" class="btn btn-outline-light btn-lg">Get In Touch</a>
                </div>
            </div>
        </div>
    </section>

    <section id="about" class="py-5">
        <div class="container">
            <h2 class="text-center mb-5 section-title">About Me</h2>
            <div class="row align-items-center">
                <div class="col-md-6">
                    <img src="{% static 'image/luffy.webp' %}" alt="Profile Picture" class="img-fluid rounded-circle mb-4 mb-md-0">
                </div>
                <div class="col-md-6">
                    <p class="lead">Hello! I'm Salina, a student with a passion for AI and full-stack development.</p>
                    <p>My journey in technology started with a fascination for how AI can solve complex problems and how full-stack development can bring those solutions to life. I love transforming innovative ideas into robust, scalable, and user-friendly applications.</p>
                    <p>When I'm not coding, you can find me exploring new AI models and contributing to open-source projects. I'm always eager to learn new technologies and collaborate on exciting projects. Let's build something amazing together!</p>
                </div>
            </div>
        </div>
    </section>

    <section id="skills" class="bg-light py-5">
        <div class="container">
            <h2 class="text-center mb-5 section-title">My Skills</h2>
            <div class="row text-center">
                <div class="col-md-4 mb-4">
                    <div class="skill-card p-4 rounded shadow-sm">
                        <i class="fas fa-code fa-3x mb-3 text-primary"></i>
                        <h5 class="fw-bold">Frontend Development</h5>
                        <p>HTML5, CSS3, JavaScript, React, Next.js, Bootstrap, Responsive Design</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="skill-card p-4 rounded shadow-sm">
                        <i class="fas fa-server fa-3x mb-3 text-primary"></i>
                        <h5 class="fw-bold">Backend Development</h5>
                        <p>Python (Django, Flask), Node.js (Express), REST APIs</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="skill-card p-4 rounded shadow-sm">
                        <i class="fas fa-database fa-3x mb-3 text-primary"></i>
                        <h5 class="fw-bold">Database Management</h5>
                        <p>PostgreSQL, MySQL, MongoDB, SQLite</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="skill-card p-4 rounded shadow-sm">
                        <i class="fas fa-cloud fa-3x mb-3 text-primary"></i>
                        <h5 class="fw-bold">Cloud & DevOps</h5>
                        <p>AWS, Docker, Git, CI/CD</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="skill-card p-4 rounded shadow-sm">
                        <i class="fas fa-brain fa-3x mb-3 text-primary"></i>
                        <h5 class="fw-bold">AI/ML Integration</h5>
                        <p>TensorFlow, PyTorch, Scikit-learn, Natural Language Processing</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="skill-card p-4 rounded shadow-sm">
                        <i class="fas fa-chart-bar fa-3x mb-3 text-primary"></i>
                        <h5 class="fw-bold">Data Analysis</h5>
                        <p>Pandas, NumPy, Matplotlib, Data Visualization</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="portfolio" class="py-5">
        <div class="container">
            <h2 class="text-center mb-5 section-title">My Latest Work</h2>
            <div class="row">
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="portfolio-item card shadow-sm">
                        <img src="{% static 'image/luffy.webp' %}" class="card-img-top" alt="Project 1">
                        <div class="card-body">
                            <h5 class="card-title">Project Title One</h5>
                            <p class="card-text">A brief description of Project One, highlighting key features or technologies used.</p>
                            <a href="#" class="btn btn-sm btn-outline-primary">View Details</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="portfolio-item card shadow-sm">
                        <img src="{% static 'image/luffy.webp' %}" class="card-img-top" alt="Project 2">
                        <div class="card-body">
                            <h5 class="card-title">Project Title Two</h5>
                            <p class="card-text">A brief description of Project Two, highlighting key features or technologies used.</p>
                            <a href="#" class="btn btn-sm btn-outline-primary">View Details</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="portfolio-item card shadow-sm">
                        <img src="{% static 'image/luffy.webp' %}" class="card-img-top" alt="Project 3">
                        <div class="card-body">
                            <h5 class="card-title">Project Title Three</h5>
                            <p class="card-text">A brief description of Project Three, highlighting key features or technologies used.</p>
                            <a href="#" class="btn btn-sm btn-outline-primary">View Details</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="portfolio-item card shadow-sm">
                        <img src="{% static 'image/luffy.webp' %}" class="card-img-top" alt="Project 4">
                        <div class="card-body">
                            <h5 class="card-title">Project Title Four</h5>
                            <p class="card-text">A brief description of Project Four, highlighting key features or technologies used.</p>
                            <a href="#" class="btn btn-sm btn-outline-primary">View Details</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="portfolio-item card shadow-sm">
                        <img src="{% static 'image/luffy.webp' %}" class="card-img-top" alt="Project 5">
                        <div class="card-body">
                            <h5 class="card-title">Project Title Five</h5>
                            <p class="card-text">A brief description of Project Five, highlighting key features or technologies used.</p>
                            <a href="#" class="btn btn-sm btn-outline-primary">View Details</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="portfolio-item card shadow-sm">
                        <img src="{% static 'image/luffy.webp' %}" class="card-img-top" alt="Project 6">
                        <div class="card-body">
                            <h5 class="card-title">Project Title Six</h5>
                            <p class="card-text">A brief description of Project Six, highlighting key features or technologies used.</p>
                            <a href="#" class="btn btn-sm btn-outline-primary">View Details</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="contact" class="bg-dark text-white py-5">
        <div class="container">
            <h2 class="text-center mb-5 section-title text-white">Get In Touch</h2>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <p class="text-center mb-4">Have a question or want to work together? Fill out the form below or reach out directly!</p>
                    <form>
                        <div class="mb-3">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email: <EMAIL></label>
                            <input type="email" class="form-control" id="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">Message</label>
                            <textarea class="form-control" id="message" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary btn-lg w-100">Send Message</button>
                    </form>
                    <div class="text-center mt-4">
                        <p>Or find me on:</p>
                        <a href="#" class="text-white mx-2"><i class="fab fa-linkedin fa-2x"></i></a>
                        <a href="#" class="text-white mx-2"><i class="fab fa-github fa-2x"></i></a>
                        <a href="#" class="text-white mx-2"><i class="fab fa-twitter fa-2x"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer class="bg-dark text-white text-center py-4">
        <div class="container">
            <p>&copy; 2024 Salina. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Custom JavaScript for animations -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all sections for scroll animations
        document.addEventListener('DOMContentLoaded', () => {
            const sections = document.querySelectorAll('section');
            sections.forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(30px)';
                section.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                observer.observe(section);
            });

            // Animate skill cards on scroll
            const skillCards = document.querySelectorAll('.skill-card');
            skillCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
                observer.observe(card);
            });

            // Animate portfolio items on scroll
            const portfolioItems = document.querySelectorAll('.portfolio-item');
            portfolioItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(50px)';
                item.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
                observer.observe(item);
            });
        });

        // Parallax effect for hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const heroSection = document.querySelector('.hero-section');
            if (heroSection) {
                heroSection.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });

        // Typing animation for hero text
        function typeWriter(element, text, speed = 100) {
            let i = 0;
            element.innerHTML = '';
            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }
            type();
        }

        // Initialize typing animation when page loads
        window.addEventListener('load', () => {
            const heroTitle = document.querySelector('.hero-section h1');
            if (heroTitle) {
                const originalText = heroTitle.textContent;
                typeWriter(heroTitle, originalText, 150);
            }
        });

        // Add floating particles effect
        function createParticle() {
            const particle = document.createElement('div');
            particle.style.position = 'fixed';
            particle.style.width = '4px';
            particle.style.height = '4px';
            particle.style.background = 'linear-gradient(45deg, #e94560, #ff6b9d)';
            particle.style.borderRadius = '50%';
            particle.style.pointerEvents = 'none';
            particle.style.zIndex = '1000';
            particle.style.opacity = '0.7';

            const startX = Math.random() * window.innerWidth;
            const startY = window.innerHeight + 10;

            particle.style.left = startX + 'px';
            particle.style.top = startY + 'px';

            document.body.appendChild(particle);

            const animation = particle.animate([
                { transform: 'translateY(0px) rotate(0deg)', opacity: 0.7 },
                { transform: `translateY(-${window.innerHeight + 100}px) rotate(360deg)`, opacity: 0 }
            ], {
                duration: Math.random() * 3000 + 2000,
                easing: 'linear'
            });

            animation.onfinish = () => particle.remove();
        }

        // Create particles periodically
        setInterval(createParticle, 300);
    </script>
</body>
</html>