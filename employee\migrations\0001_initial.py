# Generated by Django 5.2.4 on 2025-07-09 09:10

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)),
                ('middle_name', models.Char<PERSON>ield(blank=True, max_length=100)),
                ('last_name', models.Char<PERSON><PERSON>(max_length=100)),
                ('email', models.EmailField(max_length=100, unique=True)),
                ('address', models.TextField()),
                ('phone_number', models.Char<PERSON><PERSON>(blank=True, max_length=10)),
                ('image', models.ImageField(upload_to='image/')),
            ],
        ),
    ]
