#   fibonacci series
# a=1
# b=1
# print('1')
# for i  in range(100):

#     c=b
#     b=a+b
#     print(b)
#     a=c


# evennumber
# for i in range(1,101):
#     if i%2==0:
#         print(i)


# odd number
# for i in range(1,101):
#     if i%2!=0:
#         print(i)

# prime number
# for i in range(1,101):
#     for j in range(2,i):
#         if i%j==0:
#             break
#     else:
#         print(i)

# armstrong number
# for i in range(1,1000):
#     sum=0
#     temp=i
#     while temp>0:
#         digit=temp%10
#         sum+=digit**3
#         temp//=10
#     if i==sum:
#         print(i)

# perfect number
# for i in range(1,1000):
#     sum=0
#     for j in range(1,i):
#         if i%j==0:
#             sum+=j
#     if i==sum:
#         print(i)

# palindrome number
# for i in range(1,1000):
#     temp=i
#     rev=0
#     while temp>0:

# check for odd or even  numbers
# a = int(input("Enter a number: "))
# if a % 2 == 0:
#     print("Even")
# else:
#     print("Odd")

#  positive numbers
# a = int(input("Enter a number: "))
# if a > 0:
#     print("Positive")
# elif a < 0:
#     print("Negative")
# else:
#     print("Zero") 