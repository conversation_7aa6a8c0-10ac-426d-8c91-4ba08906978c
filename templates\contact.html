
{% load static %}<html lang="en">
<head>    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">    <title>Contact Me - My Modern Portfolio</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"></head>
<body>    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">        <div class="container">
            <a class="navbar-brand" href="/">My Portfolio</a>            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>            </button>
            <div class="collapse navbar-collapse" id="navbarNav">                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">                        <a class="nav-link" href="/#home">Home</a>
                    </li>                    <li class="nav-item">
                        <a class="nav-link" href="/#about">About</a>                    </li>
                    <li class="nav-item">                        <a class="nav-link" href="/#portfolio">Portfolio</a>
                    </li>                    <li class="nav-item">
                        <a class="nav-link active" href="/contact">Contact</a>                    </li>
                </ul>            </div>
        </div>    </nav>
    <!-- Contact Section -->
    <section id="contact" class="bg-dark text-white py-5 mt-5">        <div class="container">
            <h2 class="text-center mb-5 section-title text-white">Get In Touch</h2>            <div class="row">
                <div class="col-lg-6 mb-4">                    <div class="contact-info p-4 rounded" style="background: rgba(15, 52, 96, 0.5);">
                        <h3 class="mb-4">Contact Information</h3>                        <div class="d-flex mb-3">
                            <i class="fas fa-envelope me-3 text-primary"></i>                            <p><EMAIL></p>
                        </div>                        <div class="d-flex mb-3">
                            <i class="fas fa-phone me-3 text-primary"></i>                            <p>+977 ************</p>
                        </div>                        <div class="d-flex mb-3">
                            <i class="fas fa-map-marker-alt me-3 text-primary"></i>                            <p>Kathmandu, Nepal</p>
                        </div>                        <div class="social-links mt-4">
                            <a href="#" class="me-3"><i class="fab fa-linkedin fa-2x"></i></a>                            <a href="#" class="me-3"><i class="fab fa-github fa-2x"></i></a>
                            <a href="#" class="me-3"><i class="fab fa-twitter fa-2x"></i></a>                        </div>
                    </div>                </div>
                <div class="col-lg-6">                    <form class="contact-form p-4 rounded" style="background: rgba(15, 52, 96, 0.5);">
                        <div class="mb-3">                            <label for="name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="name" required>                        </div>
                        <label for="name" class="form-label">Name</label>
                        <div class="mb-3">                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" required>                        </div>
                        <div class="mb-3">                            <label for="subject" class="form-label">Subject</label>
                            <input type="text" class="form-control" id="subject" required>                        </div>
                        <div class="mb-3">                            <label for="message" class="form-label">Message</label>
                            <textarea class="form-control" id="message" rows="5" required></textarea>                        </div>
                        <button type="submit" class="btn btn-primary w-100">Send Message</button>                    </form>
                </div>            </div>
        </div>    </section>
    <!-- Employee Data Table Section -->
    <section class="py-5" style="background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);">
        <div class="container">
            <h2 class="text-center mb-5 section-title text-white">Employee Directory</h2>
            <div class="table-responsive">
                <table class="employee-table table table-dark table-striped table-hover">
                    <thead class="table-header">
                        <tr>
                            <th scope="col"><i class="fas fa-user me-2"></i>First Name</th>
                            <th scope="col"><i class="fas fa-user-tag me-2"></i>Middle Name</th>
                            <th scope="col"><i class="fas fa-user-circle me-2"></i>Last Name</th>
                            <th scope="col"><i class="fas fa-envelope me-2"></i>Email</th>
                            <th scope="col"><i class="fas fa-map-marker-alt me-2"></i>Address</th>
                            <th scope="col"><i class="fas fa-phone me-2"></i>Phone Number</th>
                            <th scope="col"><i class="fas fa-image me-2"></i>Image</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in employee_data %}
                        <tr class="employee-row">
                            <td class="employee-name">{{employee.first_name}}</td>
                            <td class="employee-name">{{employee.middle_name}}</td>
                            <td class="employee-name">{{employee.last_name}}</td>
                            <td class="employee-email">{{employee.email}}</td>
                            <td class="employee-address">{{employee.address}}</td>
                            <td class="employee-phone">{{employee.phone_number|default:"N/A"}}</td>
                            <td class="employee-image">
                                <img src="{{employee.image.url}}" alt="{{employee.first_name}}" class="employee-img">
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fas fa-users fa-3x mb-3 text-muted"></i>
                                <p class="text-muted">No employee data available</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </section>
    <!-- Footer -->
    <footer class="py-3">        <div class="container">
            <p class="text-center mb-0">&copy; 2024 My Portfolio. All rights reserved.</p>
        </div>    </footer>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
















































