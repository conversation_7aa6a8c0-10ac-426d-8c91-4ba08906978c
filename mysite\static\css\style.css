/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a2e;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #e94560, #ff6b9d);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #ff6b9d, #e94560);
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    background-attachment: fixed;
    color: #e0e0e0;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Animated background particles */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(233, 69, 96, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 107, 157, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(15, 52, 96, 0.1) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(10px) rotate(-1deg); }
}

/* General Styling with animations */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #e94560, #ff6b9d, #e94560);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 3rem;
    position: relative;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 3px;
    animation: gradientShift 3s ease-in-out infinite;
}

.section-title::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -10px;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #e94560, #ff6b9d);
    border-radius: 2px;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes pulse {
    0%, 100% { transform: translateX(-50%) scaleX(1); opacity: 1; }
    50% { transform: translateX(-50%) scaleX(1.2); opacity: 0.8; }
}

/* Enhanced Navbar */
.navbar {
    background: rgba(22, 33, 62, 0.95) !important;
    backdrop-filter: blur(15px);
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(233, 69, 96, 0.2);
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.8rem;
    background: linear-gradient(45deg, #e94560, #ff6b9d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-brand:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 0 10px rgba(233, 69, 96, 0.5));
}

.navbar-nav .nav-link {
    color: #e0e0e0 !important;
    font-weight: 500;
    margin-left: 20px;
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(45deg, #e94560, #ff6b9d);
    transition: left 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #e94560 !important;
    transform: translateY(-2px);
}

.navbar-nav .nav-link:hover::before {
    left: 0;
}

/* Enhanced Hero Section */
.hero-section {
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.8), rgba(22, 33, 62, 0.8)), url('../image/luffy.webp');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(233, 69, 96, 0.1), transparent);
    animation: shimmer 3s ease-in-out infinite;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-section h1 {
    font-size: 4.5rem;
    font-weight: 800;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #ffffff, #e94560, #ff6b9d, #ffffff);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: textGlow 4s ease-in-out infinite;
}

.hero-section p {
    font-size: 1.7rem;
    font-weight: 300;
    max-width: 800px;
    margin: 0 auto 40px auto;
    animation: fadeInUp 1s ease-out 0.5s both;
}

.hero-section .btn {
    border-radius: 50px;
    padding: 12px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 1s ease-out 1s both;
}

.hero-section .btn-primary {
    background: linear-gradient(45deg, #e94560, #ff6b9d);
    border: none;
    box-shadow: 0 5px 15px rgba(233, 69, 96, 0.4);
}

.hero-section .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.hero-section .btn-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 30px rgba(233, 69, 96, 0.6);
}

.hero-section .btn-primary:hover::before {
    left: 100%;
}

@keyframes shimmer {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

@keyframes textGlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-section .btn-outline-light {
    border-color: #e0e0e0;
    color: #e0e0e0;
}

.hero-section .btn-outline-light:hover {
    background-color: #e0e0e0;
    color: #1a1a2e;
    transform: translateY(-3px);
}

/* About Section */
#about {
    background-color: #0f3460; /* Dark blue */
    color: #e0e0e0;
}

#about img {
    width: 250px;
    height: 250px;
    object-fit: cover;
    border: 5px solid transparent;
    background: linear-gradient(45deg, #e94560, #ff6b9d, #e94560);
    background-clip: padding-box;
    border-radius: 50%;
    box-shadow:
        0 0 30px rgba(233, 69, 96, 0.5),
        inset 0 0 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    position: relative;
    animation: profileFloat 6s ease-in-out infinite;
}

#about img:hover {
    transform: scale(1.05) rotate(5deg);
    box-shadow:
        0 0 50px rgba(233, 69, 96, 0.8),
        0 20px 40px rgba(0, 0, 0, 0.3);
}

#about p {
    font-size: 1.1rem;
    animation: fadeInLeft 1s ease-out;
}

@keyframes profileFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(2deg); }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Enhanced Skills Section */
#skills {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    position: relative;
    overflow: hidden;
}

#skills::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%23e94560" opacity="0.1"/></svg>') repeat;
    animation: sparkle 10s linear infinite;
}

.skill-card {
    background: linear-gradient(145deg, #0f3460, #1a2a4e);
    color: #e0e0e0;
    border: 1px solid rgba(233, 69, 96, 0.3);
    border-radius: 15px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    perspective: 1000px;
}

.skill-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(233, 69, 96, 0.1), transparent);
    transition: left 0.5s;
}

.skill-card:hover {
    transform: translateY(-15px) rotateX(5deg) rotateY(5deg);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(233, 69, 96, 0.3);
    border-color: #e94560;
}

.skill-card:hover::before {
    left: 100%;
}

.skill-card i {
    color: #e94560;
    transition: all 0.3s ease;
    filter: drop-shadow(0 0 10px rgba(233, 69, 96, 0.5));
}

.skill-card:hover i {
    transform: scale(1.2) rotateY(360deg);
    color: #ff6b9d;
}

.skill-card h5 {
    color: #e0e0e0;
    transition: all 0.3s ease;
}

.skill-card:hover h5 {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(233, 69, 96, 0.5);
}

@keyframes sparkle {
    0% { transform: translateX(-100px) translateY(-100px); }
    100% { transform: translateX(100px) translateY(100px); }
}

/* Enhanced Portfolio Section */
#portfolio {
    background: linear-gradient(135deg, #0f3460 0%, #1a2a4e 100%);
    color: #e0e0e0;
    position: relative;
}

.portfolio-item {
    background: linear-gradient(145deg, #16213e, #1a2a4e);
    border: 1px solid rgba(233, 69, 96, 0.3);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    perspective: 1000px;
}

.portfolio-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(233, 69, 96, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.portfolio-item:hover {
    transform: translateY(-20px) rotateX(5deg) scale(1.02);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.5),
        0 0 30px rgba(233, 69, 96, 0.4);
    border-color: #e94560;
}

.portfolio-item:hover::before {
    opacity: 1;
}

.portfolio-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: all 0.4s ease;
    position: relative;
    z-index: 0;
}

.portfolio-item:hover img {
    transform: scale(1.1);
    filter: brightness(1.2) contrast(1.1);
}

.portfolio-item .card-body {
    padding: 20px;
    position: relative;
    z-index: 2;
}

.portfolio-item .card-title {
    color: #e94560;
    font-weight: 600;
    transition: all 0.3s ease;
}

.portfolio-item:hover .card-title {
    color: #ff6b9d;
    text-shadow: 0 0 10px rgba(233, 69, 96, 0.5);
}

.portfolio-item .card-text {
    color: #b0b0b0;
    transition: all 0.3s ease;
}

.portfolio-item:hover .card-text {
    color: #e0e0e0;
}

.portfolio-item .btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.portfolio-item .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.portfolio-item .btn:hover::before {
    left: 100%;
}

/* Enhanced Contact Section */
#contact {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    position: relative;
    overflow: hidden;
}

#contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(233, 69, 96, 0.1) 0%, transparent 50%);
    animation: contactGlow 8s ease-in-out infinite;
}

#contact .form-control {
    background: linear-gradient(145deg, #0f3460, #1a2a4e);
    border: 1px solid rgba(233, 69, 96, 0.3);
    color: #e0e0e0;
    border-radius: 10px;
    transition: all 0.3s ease;
    position: relative;
}

#contact .form-control:focus {
    border-color: #e94560;
    box-shadow: 0 0 20px rgba(233, 69, 96, 0.3);
    transform: translateY(-2px);
}

#contact .form-control::placeholder {
    color: #b0b0b0;
}

#contact .form-label {
    color: #e0e0e0;
    font-weight: 500;
}

#contact .btn-primary {
    background: linear-gradient(45deg, #e94560, #ff6b9d);
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#contact .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#contact .btn-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 30px rgba(233, 69, 96, 0.4);
}

#contact .btn-primary:hover::before {
    left: 100%;
}

#contact .fab {
    color: #e94560;
    transition: all 0.3s ease;
    font-size: 2rem;
}

#contact .fab:hover {
    color: #ff6b9d;
    transform: translateY(-5px) scale(1.2);
    filter: drop-shadow(0 5px 15px rgba(233, 69, 96, 0.5));
}

/* Enhanced Footer */
footer {
    background: linear-gradient(135deg, #16213e 0%, #0f3460 100%);
    color: #e0e0e0;
    padding: 1.5rem 0;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #e94560, #ff6b9d, #e94560, transparent);
    animation: footerGlow 3s ease-in-out infinite;
}

footer p {
    margin: 0;
    text-align: center;
    background: linear-gradient(45deg, #e0e0e0, #e94560, #e0e0e0);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: footerText 4s ease-in-out infinite;
}

@keyframes contactGlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

@keyframes footerGlow {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

@keyframes footerText {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 3rem;
    }

    .hero-section p {
        font-size: 1.3rem;
    }

    .section-title {
        font-size: 2rem;
    }

    #about img {
        width: 200px;
        height: 200px;
    }

    .skill-card:hover {
        transform: translateY(-10px) rotateX(2deg) rotateY(2deg);
    }

    .portfolio-item:hover {
        transform: translateY(-15px) rotateX(2deg) scale(1.01);
    }

    .navbar-brand {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }

    .hero-section p {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    #about img {
        width: 180px;
        height: 180px;
    }

    .skill-card, .portfolio-item {
        margin-bottom: 2rem;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .section-title {
        -webkit-text-fill-color: #ffffff;
        color: #ffffff;
    }

    .skill-card, .portfolio-item {
        border-width: 2px;
    }
}

/* Ultra Compact Employee Table Styling */
.employee-table {
    background: linear-gradient(145deg, #16213e, #1a2a4e) !important;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(233, 69, 96, 0.3);
    margin-bottom: 0;
    font-size: 0.65rem;
    max-width: 700px;
    margin: 0 auto;
}

.table-header {
    background: linear-gradient(45deg, #e94560, #ff6b9d) !important;
}

.table-header th {
    color: #ffffff !important;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    padding: 6px 4px;
    border: none !important;
    position: relative;
    font-size: 0.6rem;
}

.table-header th i {
    color: #ffffff;
    opacity: 0.9;
    font-size: 0.55rem;
}

.employee-row {
    transition: all 0.3s ease;
    border: none !important;
}

.employee-row:hover {
    background: linear-gradient(145deg, #1a2a4e, #0f3460) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(233, 69, 96, 0.2);
}

.employee-row td {
    padding: 4px 3px;
    border: none !important;
    color: #e0e0e0;
    vertical-align: middle;
    position: relative;
    font-size: 0.6rem;
}

.employee-name {
    font-weight: 500;
    color: #ffffff !important;
}

.employee-email {
    color: #e94560 !important;
    font-family: 'Courier New', monospace;
}

.employee-address {
    color: #b0b0b0 !important;
    font-size: 0.9rem;
}

.employee-phone {
    color: #ff6b9d !important;
    font-weight: 500;
}

.employee-image {
    text-align: center;
}

.employee-img {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid #e94560;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.employee-img:hover {
    transform: scale(1.2);
    border-color: #ff6b9d;
    box-shadow: 0 2px 5px rgba(233, 69, 96, 0.4);
}

/* Table responsive enhancements */
.table-responsive {
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Empty state styling */
.employee-table tbody tr td[colspan="7"] {
    background: linear-gradient(145deg, #16213e, #1a2a4e) !important;
    color: #b0b0b0;
    padding: 40px;
}

/* Mobile responsiveness for ultra compact table */
@media (max-width: 768px) {
    .employee-table {
        font-size: 0.55rem;
        max-width: 100%;
    }

    .table-header th {
        padding: 4px 2px;
        font-size: 0.5rem;
    }

    .employee-row td {
        padding: 3px 2px;
    }

    .employee-img {
        width: 8px;
        height: 8px;
    }
}

@media (max-width: 576px) {
    .employee-table {
        font-size: 0.5rem;
    }

    .table-header th {
        padding: 3px 1px;
        font-size: 0.45rem;
    }

    .employee-row td {
        padding: 2px 1px;
    }

    .employee-img {
        width: 6px;
        height: 6px;
        border-width: 1px;
    }

    .table-header th i {
        font-size: 0.4rem;
    }
}