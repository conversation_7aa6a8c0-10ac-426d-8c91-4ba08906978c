body {
    font-family: 'Montserrat', sans-serif; /* Using a modern font */
    background-color: #1a1a2e; /* Dark background */
    color: #e0e0e0; /* Light text color */
    line-height: 1.6;
}

/* General Styling */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #e94560; /* Accent color */
    margin-bottom: 3rem;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -10px;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background-color: #e94560;
    border-radius: 2px;
}

/* Navbar */
.navbar {
    background-color: #16213e !important; /* Darker blue for navbar */
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.8rem;
    color: #e0e0e0 !important;
}

.navbar-nav .nav-link {
    color: #e0e0e0 !important;
    font-weight: 500;
    margin-left: 20px;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #e94560 !important; /* Accent color on hover */
}

/* Hero Section */
.hero-section {
    background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-section h1 {
    font-size: 4.5rem;
    font-weight: 800;
    margin-bottom: 20px;
}

.hero-section p {
    font-size: 1.7rem;
    font-weight: 300;
    max-width: 800px;
    margin: 0 auto 40px auto;
}

.hero-section .btn {
    border-radius: 50px;
    padding: 12px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.hero-section .btn-primary {
    background-color: #e94560;
    border-color: #e94560;
}

.hero-section .btn-primary:hover {
    background-color: #c73a50;
    border-color: #c73a50;
    transform: translateY(-3px);
}

.hero-section .btn-outline-light {
    border-color: #e0e0e0;
    color: #e0e0e0;
}

.hero-section .btn-outline-light:hover {
    background-color: #e0e0e0;
    color: #1a1a2e;
    transform: translateY(-3px);
}

/* About Section */
#about {
    background-color: #0f3460; /* Dark blue */
    color: #e0e0e0;
}

#about img {
    width: 250px;
    height: 250px;
    object-fit: cover;
    border: 5px solid #e94560;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

#about p {
    font-size: 1.1rem;
}

/* Skills Section */
#skills {
    background-color: #1a1a2e;
}

.skill-card {
    background-color: #0f3460;
    color: #e0e0e0;
    border: 1px solid #e94560;
    transition: all 0.3s ease;
}

.skill-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
}

.skill-card i {
    color: #e94560;
}

.skill-card h5 {
    color: #e0e0e0;
}

/* Portfolio Section */
#portfolio {
    background-color: #0f3460;
    color: #e0e0e0;
}

.portfolio-item {
    background-color: #16213e;
    border: 1px solid #e94560;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
}

.portfolio-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.portfolio-item .card-body {
    padding: 20px;
}

.portfolio-item .card-title {
    color: #e94560;
    font-weight: 600;
}

.portfolio-item .card-text {
    color: #b0b0b0;
}

/* Contact Section */
#contact {
    background-color: #1a1a2e;
}

#contact .form-control {
    background-color: #0f3460;
    border: 1px solid #e94560;
    color: #e0e0e0;
}

#contact .form-control::placeholder {
    color: #b0b0b0;
}

#contact .form-label {
    color: #e0e0e0;
}

#contact .btn-primary {
    background-color: #e94560;
    border-color: #e94560;
}

#contact .btn-primary:hover {
    background-color: #c73a50;
    border-color: #c73a50;
}

#contact .fab {
    color: #e94560;
    transition: color 0.3s ease;
}

#contact .fab:hover {
    color: #e0e0e0;
}

/* Footer */
footer {
    background-color: #16213e;
    color: #e0e0e0;
    padding: 1.5rem 0;
    font-size: 0.9rem;
}