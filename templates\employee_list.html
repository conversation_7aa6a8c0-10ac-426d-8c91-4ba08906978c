{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee Directory - <PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">Salina Kunwar</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/#portfolio">Portfolio</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/contact/">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/employees/">Employees</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Employee Directory Section -->
    <section class="py-5 mt-5" style="background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); min-height: 100vh;">
        <div class="container">
            <div class="text-center mb-5">
                <h1 class="section-title text-white mb-4">
                    <i class="fas fa-users me-3"></i>Employee Directory
                </h1>
                <p class="lead text-light">Meet our amazing team members</p>
            </div>
            
            <div class="table-responsive">
                <table class="employee-table table table-dark table-striped table-hover">
                    <thead class="table-header">
                        <tr>
                            <th scope="col"><i class="fas fa-user me-2"></i>First Name</th>
                            <th scope="col"><i class="fas fa-user-tag me-2"></i>Middle Name</th>
                            <th scope="col"><i class="fas fa-user-circle me-2"></i>Last Name</th>
                            <th scope="col"><i class="fas fa-envelope me-2"></i>Email</th>
                            <th scope="col"><i class="fas fa-map-marker-alt me-2"></i>Address</th>
                            <th scope="col"><i class="fas fa-phone me-2"></i>Phone Number</th>
                            <th scope="col"><i class="fas fa-image me-2"></i>Image</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employee in employee_data %}
                        <tr class="employee-row">
                            <td class="employee-name">{{employee.first_name}}</td>
                            <td class="employee-name">{{employee.middle_name}}</td>
                            <td class="employee-name">{{employee.last_name}}</td>
                            <td class="employee-email">{{employee.email}}</td>
                            <td class="employee-address">{{employee.address}}</td>
                            <td class="employee-phone">{{employee.phone_number|default:"N/A"}}</td>
                            <td class="employee-image">
                                <img src="{{employee.image.url}}" alt="{{employee.first_name}}" class="employee-img">
                            </td>
                        </tr>    
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center py-5">
                                <i class="fas fa-users fa-4x mb-4 text-muted"></i>
                                <h4 class="text-muted mb-3">No Employees Found</h4>
                                <p class="text-muted">There are currently no employees in the database.</p>
                                <a href="/admin/" class="btn btn-primary mt-3">
                                    <i class="fas fa-plus me-2"></i>Add Employees
                                </a>
                            </td>
                        </tr>
                        {% endfor %}    
                    </tbody>
                </table>
            </div>

            <!-- Statistics Section -->
            <div class="row mt-5">
                <div class="col-md-4 mb-3">
                    <div class="card bg-dark border-primary">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x text-primary mb-3"></i>
                            <h5 class="card-title text-white">Total Employees</h5>
                            <h2 class="text-primary">{{ employee_data|length }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card bg-dark border-success">
                        <div class="card-body text-center">
                            <i class="fas fa-building fa-2x text-success mb-3"></i>
                            <h5 class="card-title text-white">Departments</h5>
                            <h2 class="text-success">{{ employee_data|length|add:"0"|default:"1" }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card bg-dark border-warning">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-line fa-2x text-warning mb-3"></i>
                            <h5 class="card-title text-white">Active</h5>
                            <h2 class="text-warning">{{ employee_data|length }}</h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-3">
        <div class="container">
            <p class="text-center mb-0">&copy; 2024 Salina Kunwar. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript for table animations -->
    <script>
        // Animate table rows on load
        document.addEventListener('DOMContentLoaded', function() {
            const rows = document.querySelectorAll('.employee-row');
            rows.forEach((row, index) => {
                row.style.opacity = '0';
                row.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    row.style.transition = 'all 0.5s ease';
                    row.style.opacity = '1';
                    row.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
